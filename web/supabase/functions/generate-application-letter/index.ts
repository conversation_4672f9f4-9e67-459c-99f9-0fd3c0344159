/// <reference types="https://deno.land/x/types/index.d.ts" />
declare const Deno: any;

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { GoogleGenerativeAI } from "https://esm.sh/@google/generative-ai@0.17.1"

// Types for letter generation
interface StructuredLetterData {
  metadata: {
    generatedAt: string;
    lastModified: string;
    templateId: string;
    language: string;
  };
  header: {
    date: string;
    formattedDate?: string;
  };
  subject: {
    prefix: string;
    position: string;
  };
  recipient: {
    salutation: string;
    title: string;
    company?: string;
    address?: string[];
  };
  body: {
    opening: string;
    paragraphs: string[];
    closing: string;
  };
  signature: {
    farewell: string;
    name: string;
  };
  attachments?: string[];
}

interface ResumeInput {
  file?: {
    buffer?: string; // base64 encoded
    extractedText?: string;
    mimeType: string;
  };
  manual?: {
    fullName: string;
    professionalTitle: string;
    professionalSummary: string;
    mostRecentJob: {
      title: string;
      company: string;
      achievements: string;
    };
    skills: string;
  };
}

interface JobInput {
  description?: string;
  image?: {
    buffer: string; // base64 encoded
    mimeType: string;
  };
}

interface LetterTemplate {
  id: string;
  name: string;
  previewDescription: string;
  templateHtml: string | ((data: StructuredLetterData) => string);
  previewImagePath: string;
  isPremium: boolean;
  recommended: boolean;
  tokenCost: number;
  fontFamily: string;
}

interface GenerateApplicationLetterRequest {
  jobDescription?: string;
  jobImage?: {
    buffer: string;
    mimeType: string;
  };
  unauthenticatedResumeFile?: {
    buffer: string;
    mimeType: string;
  };
  unauthenticatedResumeFileName?: string;
  templateId?: string;
  existingLetterId?: string;
  editedStructuredData?: StructuredLetterData;
}

/**
 * Letter template collection - simplified for Edge Function
 */
const applicationLetterTemplates: LetterTemplate[] = [
  {
    id: 'plain-text',
    name: 'Plain Text',
    previewDescription: 'Simple plain text format',
    templateHtml: '',
    previewImagePath: '',
    isPremium: false,
    recommended: true,
    tokenCost: 0,
    fontFamily: 'Arial, sans-serif'
  },
  {
    id: 'classic-blue',
    name: 'Classic Blue',
    previewDescription: 'Professional blue theme',
    templateHtml: '',
    previewImagePath: '',
    isPremium: false,
    recommended: false,
    tokenCost: 5,
    fontFamily: 'Arial, sans-serif'
  },
  {
    id: 'professional-classic',
    name: 'Professional Classic',
    previewDescription: 'Classic professional layout',
    templateHtml: '',
    previewImagePath: '',
    isPremium: true,
    recommended: false,
    tokenCost: 10,
    fontFamily: 'Times New Roman, serif'
  }
];

/**
 * Get template by ID
 */
function getTemplateById(id: string): LetterTemplate | undefined {
  return applicationLetterTemplates.find(template => template.id === id);
}

/**
 * Convert structured letter data to plain text format
 */
function convertStructuredDataToPlainText(data: StructuredLetterData): string {
  const sections: string[] = [];

  // Add date
  if (data.header?.date) {
    sections.push(data.header.formattedDate || data.header.date);
  }

  // Add subject
  if (data.subject) {
    const subject = data.subject.prefix ?
      `${data.subject.prefix} ${data.subject.position}` :
      data.subject.position;
    if (subject) {
      sections.push(subject);
    }
  }

  // Add recipient
  if (data.recipient) {
    const recipientLines: string[] = [];
    if (data.recipient.salutation && data.recipient.title) {
      recipientLines.push(`${data.recipient.salutation} ${data.recipient.title}`);
    }
    if (data.recipient.company) {
      recipientLines.push(data.recipient.company);
    }
    if (data.recipient.address) {
      recipientLines.push(...data.recipient.address);
    }
    if (recipientLines.length > 0) {
      sections.push(recipientLines.join('\n'));
    }
  }

  // Add opening
  if (data.body?.opening) {
    sections.push(data.body.opening);
  }

  // Add paragraphs
  if (data.body?.paragraphs && data.body.paragraphs.length > 0) {
    sections.push(...data.body.paragraphs);
  }

  // Add closing
  if (data.body?.closing) {
    sections.push(data.body.closing);
  }

  // Add signature
  if (data.signature) {
    const signatureLines: string[] = [];
    if (data.signature.farewell) {
      signatureLines.push(data.signature.farewell);
    }
    if (data.signature.name) {
      signatureLines.push(data.signature.name);
    }
    if (signatureLines.length > 0) {
      sections.push(signatureLines.join('\n'));
    }
  }

  return sections.join('\n\n');
}

/**
 * Convert structured data to template-compatible format
 */
function convertToLetterTemplateData(data: StructuredLetterData): any {
  // Build recipient lines array
  const recipientLines: string[] = [];

  if (data.recipient.salutation && data.recipient.title) {
    recipientLines.push(`${data.recipient.salutation} ${data.recipient.title}`);
  }

  if (data.recipient.company) {
    recipientLines.push(data.recipient.company);
  }

  if (data.recipient.address) {
    recipientLines.push(...data.recipient.address);
  }

  // Combine subject prefix and position
  const subject = data.subject.prefix ?
    `${data.subject.prefix} ${data.subject.position}` :
    data.subject.position;

  return {
    date: data.header.formattedDate || data.header.date,
    subject,
    recipientLines,
    opening: data.body.opening,
    paragraphs: data.body.paragraphs,
    closing: data.body.closing,
    farewell: data.signature.farewell,
    signatureName: data.signature.name,
    additionalInfo: data.attachments?.join(', ')
  };
}

/**
 * Simple Handlebars-like template engine for Deno
 */
function renderTemplate(templateHtml: string, templateData: any): string {
  let rendered = templateHtml;

  // Replace simple variables {{variable}}
  rendered = rendered.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return templateData[key] || '';
  });

  // Handle {{#each array}} loops
  rendered = rendered.replace(/\{\{#each\s+(\w+)\}\}([\s\S]*?)\{\{\/each\}\}/g, (match, arrayName, content) => {
    const array = templateData[arrayName];
    if (Array.isArray(array)) {
      return array.map((item, index) => {
        let itemContent = content;
        // Replace {{this}} with the current item
        itemContent = itemContent.replace(/\{\{this\}\}/g, item);
        // Handle @first and @last
        itemContent = itemContent.replace(/\{\{#if @first\}\}([\s\S]*?)\{\{\/if\}\}/g, index === 0 ? '$1' : '');
        itemContent = itemContent.replace(/\{\{#unless @last\}\}([\s\S]*?)\{\{\/unless\}\}/g, index < array.length - 1 ? '$1' : '');
        return itemContent;
      }).join('');
    }
    return '';
  });

  // Handle {{#if condition}} blocks
  rendered = rendered.replace(/\{\{#if\s+(\w+)\}\}([\s\S]*?)\{\{\/if\}\}/g, (match, condition, content) => {
    return templateData[condition] ? content : '';
  });

  // Handle {{#unless condition}} blocks
  rendered = rendered.replace(/\{\{#unless\s+(\w+)\}\}([\s\S]*?)\{\{\/unless\}\}/g, (match, condition, content) => {
    return !templateData[condition] ? content : '';
  });

  return rendered;
}

/**
 * Get template HTML by ID
 */
function getTemplateHtml(templateId: string): string {
  // Basic templates - these would ideally be loaded from files
  const templates: Record<string, string> = {
    'plain-text': `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; font-weight: bold; font-size: 18px; margin-bottom: 20px; }
        .date { text-align: right; margin-bottom: 20px; }
        .subject { font-weight: bold; margin-bottom: 20px; }
        .body-text { margin-bottom: 15px; text-align: justify; }
        .signature { margin-top: 30px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>SURAT LAMARAN KERJA</h1>
    </div>
    <div class="date">
        <p>{{date}}</p>
    </div>
    <div class="subject">
        <p>{{subject}}</p>
    </div>
    <div class="salutation">
        <p>
            {{#each recipientLines}}
            <span>{{this}}</span>{{#unless @last}}<br>{{/unless}}
            {{/each}}
        </p>
    </div>
    <div class="body-text">
        <p>{{opening}}</p>
    </div>
    {{#each paragraphs}}
    <div class="body-text">
        <p>{{this}}</p>
    </div>
    {{/each}}
    {{#if closing}}
    <div class="body-text">
        <p>{{closing}}</p>
    </div>
    {{/if}}
    <div class="signature">
        <p>{{farewell}}</p>
        <br><br>
        <p>{{signatureName}}</p>
        {{#if additionalInfo}}
        <p><small>{{additionalInfo}}</small></p>
        {{/if}}
    </div>
</body>
</html>`,
    'classic-blue': `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; border-bottom: 2px solid #2563eb; padding-bottom: 15px; margin-bottom: 30px; }
        .header h1 { color: #1e3a8a; font-size: 20px; font-weight: bold; letter-spacing: 2px; }
        .date { text-align: right; margin-bottom: 20px; color: #374151; font-weight: 500; }
        .subject { font-weight: 600; color: #374151; margin-bottom: 20px; }
        .body-text { margin-bottom: 15px; text-align: justify; color: #374151; }
        .signature { margin-top: 30px; color: #374151; }
    </style>
</head>
<body>
    <div class="header">
        <h1>SURAT LAMARAN KERJA</h1>
    </div>
    <div class="date">
        <p>{{date}}</p>
    </div>
    <div class="subject">
        <p>{{subject}}</p>
    </div>
    <div class="salutation">
        <p>
            {{#each recipientLines}}
            <span>{{this}}</span>{{#unless @last}}<br>{{/unless}}
            {{/each}}
        </p>
    </div>
    <div class="body-text">
        <p>{{opening}}</p>
    </div>
    {{#each paragraphs}}
    <div class="body-text">
        <p>{{this}}</p>
    </div>
    {{/each}}
    {{#if closing}}
    <div class="body-text">
        <p>{{closing}}</p>
    </div>
    {{/if}}
    <div class="signature">
        <p>{{farewell}}</p>
        <br><br>
        <p>{{signatureName}}</p>
        {{#if additionalInfo}}
        <p><small>{{additionalInfo}}</small></p>
        {{/if}}
    </div>
</body>
</html>`
  };

  return templates[templateId] || templates['plain-text'];
}

/**
 * Fill letter template with structured data
 */
function fillLetterTemplate(template: LetterTemplate, data: StructuredLetterData): string {
  try {
    // Convert structured data to template format
    const templateData = convertToLetterTemplateData(data);

    // Get template HTML
    const templateHtml = getTemplateHtml(template.id);

    // Render template with data
    return renderTemplate(templateHtml, templateData);
  } catch (error) {
    console.error('Template rendering error:', error);
    // Fallback to plain text
    const plainText = convertStructuredDataToPlainText(data);
    return `<div style="font-family: ${template.fontFamily}; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px;">
      <pre style="white-space: pre-wrap; font-family: inherit;">${plainText}</pre>
    </div>`;
  }
}

/**
 * Delete PDF from Supabase storage
 */
async function deletePdfFromStorage(supabase: any, letterId: string): Promise<boolean> {
  try {
    const filePath = `${letterId}.pdf`;
    const { error } = await supabase.storage
      .from('generated-letters')
      .remove([filePath]);

    if (error) {
      console.error('Error deleting PDF from storage:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error deleting PDF from storage:', error);
    return false;
  }
}

/**
 * Parse FormData from request
 */
async function parseFormData(request: Request): Promise<GenerateApplicationLetterRequest> {
  const formData = await request.formData();

  const result: GenerateApplicationLetterRequest = {};

  // Parse text fields
  const jobDescription = formData.get('jobDescription') as string | null;
  if (jobDescription) result.jobDescription = jobDescription;

  const templateId = formData.get('templateId') as string | null;
  if (templateId) result.templateId = templateId;

  const existingLetterId = formData.get('existingLetterId') as string | null;
  if (existingLetterId) result.existingLetterId = existingLetterId;

  const unauthenticatedResumeFileName = formData.get('unauthenticatedResumeFileName') as string | null;
  if (unauthenticatedResumeFileName) result.unauthenticatedResumeFileName = unauthenticatedResumeFileName;

  const editedStructuredDataStr = formData.get('editedStructuredData') as string | null;
  if (editedStructuredDataStr) {
    try {
      result.editedStructuredData = JSON.parse(editedStructuredDataStr);
    } catch (error) {
      console.error('Failed to parse editedStructuredData:', error);
    }
  }

  // Parse file fields
  const jobImage = formData.get('jobImage') as File | null;
  if (jobImage) {
    const buffer = await jobImage.arrayBuffer();
    const base64 = btoa(String.fromCharCode(...new Uint8Array(buffer)));
    result.jobImage = {
      buffer: base64,
      mimeType: jobImage.type
    };
  }

  const unauthenticatedResumeFile = formData.get('unauthenticatedResumeFile') as File | null;
  if (unauthenticatedResumeFile) {
    const buffer = await unauthenticatedResumeFile.arrayBuffer();
    const base64 = btoa(String.fromCharCode(...new Uint8Array(buffer)));
    result.unauthenticatedResumeFile = {
      buffer: base64,
      mimeType: unauthenticatedResumeFile.type
    };
  }

  return result;
}

/**
 * Get MIME type from file extension
 */
function getMimeTypeFromFileName(fileName: string): string {
  const lowerFileName = fileName.toLowerCase();

  if (lowerFileName.endsWith('.pdf')) {
    return 'application/pdf';
  } else if (lowerFileName.endsWith('.docx')) {
    return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
  } else if (lowerFileName.endsWith('.png')) {
    return 'image/png';
  } else if (lowerFileName.endsWith('.jpg') || lowerFileName.endsWith('.jpeg')) {
    return 'image/jpeg';
  } else {
    return 'text/plain';
  }
}

/**
 * Prepare resume input from form data
 */
async function prepareResumeInput(
  supabase: any,
  userId: string,
  unauthenticatedResumeFile?: { buffer: string; mimeType: string },
  unauthenticatedResumeFileName?: string
): Promise<ResumeInput> {
  const resumeInput: ResumeInput = {};

  if (unauthenticatedResumeFile && unauthenticatedResumeFileName) {
    // Use uploaded file
    const mimeType = getMimeTypeFromFileName(unauthenticatedResumeFileName);
    resumeInput.file = {
      buffer: unauthenticatedResumeFile.buffer,
      mimeType
    };
  } else {
    // Get resume from user profile
    const { data: profile, error: fetchError } = await supabase
      .from('profiles')
      .select('resume_file_name')
      .eq('id', userId)
      .single();

    if (fetchError || !profile || !profile.resume_file_name) {
      throw new Error('Resume tidak ditemukan. Harap unggah resume terlebih dahulu.');
    }

    // Get the resume file from storage
    const { data: resumeFile, error: storageError } = await supabase.storage
      .from('resumes')
      .download(profile.resume_file_name);

    if (storageError || !resumeFile) {
      throw new Error('Tidak dapat mengakses file resume. Harap unggah ulang resume Anda.');
    }

    // Convert the file to buffer and determine correct mime type
    const buffer = await resumeFile.arrayBuffer();
    const base64 = btoa(String.fromCharCode(...new Uint8Array(buffer)));
    const mimeType = getMimeTypeFromFileName(profile.resume_file_name);

    resumeInput.file = {
      buffer: base64,
      mimeType
    };
  }

  return resumeInput;
}

/**
 * Prepare job input from form data
 */
function prepareJobInput(
  jobDescription?: string,
  jobImage?: { buffer: string; mimeType: string }
): JobInput {
  const jobInput: JobInput = {};

  if (jobDescription) {
    jobInput.description = jobDescription;
  }

  if (jobImage) {
    jobInput.image = {
      buffer: jobImage.buffer,
      mimeType: jobImage.mimeType
    };
  }

  return jobInput;
}

/**
 * Main Edge Function handler
 */
serve(async (req: any) => {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-supabase-auth',
  }

  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('=== Generate Application Letter Edge Function Started ===')
    console.log('Request method:', req.method)
    console.log('Request URL:', req.url)

    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    const googleAIKey = Deno.env.get('GOOGLE_AI_API_KEY') ?? ''

    if (!supabaseUrl || !supabaseServiceKey || !googleAIKey) {
      const missingVars = []
      if (!supabaseUrl) missingVars.push('SUPABASE_URL')
      if (!supabaseServiceKey) missingVars.push('SUPABASE_SERVICE_ROLE_KEY')
      if (!googleAIKey) missingVars.push('GOOGLE_AI_API_KEY')

      const errorMsg = `Missing required environment variables: ${missingVars.join(', ')}`
      console.error('Environment error:', errorMsg)
      throw new Error(errorMsg)
    }

    // Create Supabase client
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Parse FormData
    const formData = await parseFormData(req);
    console.log('Parsed form data keys:', Object.keys(formData));

    // Validation
    if (!formData.jobDescription && !formData.jobImage) {
      return new Response(
        JSON.stringify({
          error: 'Deskripsi pekerjaan atau gambar lowongan diperlukan'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Get access token from request header
    const accessToken = req.headers.get('x-supabase-auth');

    if (!accessToken) {
      return new Response(
        JSON.stringify({ error: 'Missing authentication token' }),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Get user with the provided token
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken);

    if (userError || !user) {
      console.error('Error getting user with token:', userError);
      return new Response(
        JSON.stringify({
          error: 'Anda harus login untuk menggunakan fitur ini'
        }),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    const userId = user.id;

    // Get user profile and tokens
    const { data: profile, error: fetchError } = await supabase
      .from('profiles')
      .select('resume_file_name, tokens')
      .eq('id', userId)
      .single();

    if (fetchError || !profile) {
      return new Response(
        JSON.stringify({
          error: 'Kesalahan dalam mengambil data profil'
        }),
        {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Get template and validate
    const templateId = formData.templateId || 'plain-text';
    const selectedTemplate = getTemplateById(templateId);

    if (!selectedTemplate) {
      return new Response(
        JSON.stringify({ error: 'Template tidak ditemukan' }),
        {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Check token balance against template cost
    const requiredTokens = selectedTemplate?.tokenCost ?? 0;
    const currentTokens = (profile.tokens as number | null) ?? 0;

    if (!formData.existingLetterId && !formData.editedStructuredData && requiredTokens > 0 && currentTokens < requiredTokens) {
      return new Response(
        JSON.stringify({
          error: 'Token Anda tidak cukup untuk menggunakan template ini. Silakan top up token Anda.'
        }),
        {
          status: 402,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Handle existing letter editing
    if (formData.existingLetterId && formData.editedStructuredData) {
      const plainText = convertStructuredDataToPlainText(formData.editedStructuredData);
      const designHtml = fillLetterTemplate(selectedTemplate, formData.editedStructuredData);

      const { error: saveError } = await supabase
        .from('letters')
        .update({
          structured_data: formData.editedStructuredData,
          design_html: designHtml,
          plain_text: plainText,
          updated_at: new Date().toISOString()
        })
        .eq('id', formData.existingLetterId)
        .eq('user_id', userId)
        .select('id')
        .single();

      if (saveError) {
        console.error('Error updating letter:', saveError);
      } else {
        // Delete cached PDF from storage since the letter content has been updated
        const deletionResult = await deletePdfFromStorage(supabase, formData.existingLetterId);
        if (deletionResult) {
          console.log(`Cached PDF deleted for letter ${formData.existingLetterId}`);
        } else {
          console.warn(`Failed to delete cached PDF for letter ${formData.existingLetterId}`);
        }
      }

      return new Response(
        JSON.stringify({
          data: {
            structuredData: formData.editedStructuredData,
            design: designHtml,
            templateId,
            letterId: formData.existingLetterId,
          }
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Prepare inputs for new letter generation
    const resumeInput = await prepareResumeInput(
      supabase,
      userId,
      formData.unauthenticatedResumeFile,
      formData.unauthenticatedResumeFileName
    );

    const jobInput = prepareJobInput(formData.jobDescription, formData.jobImage);

    // Create database record with 'processing' status in letters table
    const { data: insertData, error: insertError } = await supabase
      .from('letters')
      .insert({
        user_id: userId,
        template_id: templateId,
        status: 'processing',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select('id')
      .single();

    if (insertError || !insertData) {
      console.error('Supabase insert error:', insertError);
      return new Response(
        JSON.stringify({ error: 'Failed to create letter generation record' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    const letterId = insertData.id as string;

    // Call the existing generate-letter edge function
    try {
      console.log('Calling generate-letter edge function...');

      const generateLetterRequest = {
        letterId,
        resumeInput,
        jobInput,
        templateId
      };

      // Call the generate-letter edge function
      const { data: letterData, error: letterError } = await supabase.functions.invoke('generate-letter', {
        body: generateLetterRequest,
      });

      if (letterError) {
        console.error('Letter generation error:', letterError);
        throw new Error(`Letter generation failed: ${letterError.message}`);
      }

      if (!letterData || !letterData.success) {
        console.error('Letter generation returned error:', letterData?.error);
        throw new Error(letterData?.error || 'Letter generation failed');
      }

      // Return success response
      return new Response(
        JSON.stringify({
          id: letterId,
          success: true,
          message: 'Letter generation initiated successfully'
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );

    } catch (generationError) {
      console.error('Letter generation failed:', generationError);

      // Update letter status to error
      try {
        await supabase
          .from('letters')
          .update({
            status: 'error',
            error_message: (generationError as any).message,
            updated_at: new Date().toISOString()
          })
          .eq('id', letterId);
      } catch (updateError) {
        console.error('Failed to update letter status to error:', updateError);
      }

      return new Response(
        JSON.stringify({
          error: 'Failed to generate letter',
          details: (generationError as any).message
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

  } catch (error) {
    console.error('=== Edge Function Error ===')
    console.error('Error details:', {
      name: (error as any).name,
      message: (error as any).message,
      stack: (error as any).stack
    })

    return new Response(
      JSON.stringify({
        success: false,
        error: (error as any).message || 'Unknown error occurred',
        errorType: (error as any).name || 'UnknownError',
        timestamp: new Date().toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})
